import asyncio
import logging
import time
from typing import Optional, Dict, Any

import aiohttp
from aiohttp import (
    TraceRequestStartParams, TraceRequestEndParams, TraceRequestExceptionParams,
    TraceConnectionCreateStartParams, TraceConnectionCreateEndParams, TraceConfig
)


class JiraClientTracer:
    """Comprehensive tracing for JIRA API client to diagnose connection issues"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.request_start_times = {}
        self.connection_stats = {}

    async def on_request_start(self, session, trace_config_ctx, params: TraceRequestStartParams):
        """Track request start timing and details"""
        trace_config_ctx.start_time = time.perf_counter()
        trace_config_ctx.request_url = str(params.url)
        trace_config_ctx.request_method = params.method

        self.logger.debug(f"🚀 REQUEST START: {params.method} {params.url}")
        self.logger.debug(f"   Headers: {dict(params.headers)}")

    async def on_request_end(self, session, trace_config_ctx, params: TraceRequestEndParams):
        """Track successful request completion"""
        duration = time.perf_counter() - trace_config_ctx.start_time

        self.logger.info(
            f"✅ REQUEST SUCCESS: {trace_config_ctx.request_method} {trace_config_ctx.request_url} "
            f"-> {params.response.status} in {duration:.3f}s"
        )
        self.logger.debug(f"   Response headers: {dict(params.response.headers)}")

    async def on_request_exception(self, session, trace_config_ctx, params: TraceRequestExceptionParams):
        """Track request failures with detailed error info"""
        duration = time.perf_counter() - trace_config_ctx.start_time

        self.logger.error(
            f"❌ REQUEST FAILED: {trace_config_ctx.request_method} {trace_config_ctx.request_url} "
            f"after {duration:.3f}s"
        )
        self.logger.error(f"   Exception: {type(params.exception).__name__}: {params.exception}")

        # Detailed error analysis
        if isinstance(params.exception, aiohttp.ClientConnectorError):
            self.logger.error(f"   🔌 CONNECTION ERROR: {params.exception}")
            if hasattr(params.exception, 'os_error'):
                self.logger.error(f"   🔧 OS Error: {params.exception.os_error}")

        elif isinstance(params.exception, aiohttp.ServerTimeoutError):
            self.logger.error(f"   ⏰ SERVER TIMEOUT: {params.exception}")

        elif isinstance(params.exception, asyncio.TimeoutError):
            self.logger.error(f"   ⏰ CLIENT TIMEOUT: {params.exception}")

        elif isinstance(params.exception, aiohttp.ClientResponseError):
            self.logger.error(f"   📡 RESPONSE ERROR: Status {params.exception.status}")

    async def on_connection_create_start(self, session, trace_config_ctx, params: TraceConnectionCreateStartParams):
        """Track connection creation attempts"""
        trace_config_ctx.connection_start_time = time.perf_counter()

        # Store connection info for later use
        trace_config_ctx.connection_key = getattr(params, 'key', None)

        if hasattr(params, 'key') and params.key:
            host = getattr(params.key, 'host', 'unknown')
            port = getattr(params.key, 'port', 'unknown')
            is_ssl = getattr(params.key, 'is_ssl', False)
            self.logger.debug(f"🔌 CONNECTION CREATE START: {host}:{port} (SSL: {is_ssl})")
        else:
            self.logger.debug(f"🔌 CONNECTION CREATE START: {params}")

    async def on_connection_create_end(self, session, trace_config_ctx, params: TraceConnectionCreateEndParams):
        """Track successful connection creation"""
        duration = time.perf_counter() - trace_config_ctx.connection_start_time

        # Try to get connection info from various sources
        host_info = "unknown"
        if hasattr(trace_config_ctx, 'connection_key') and trace_config_ctx.connection_key:
            key = trace_config_ctx.connection_key
            host = getattr(key, 'host', 'unknown')
            port = getattr(key, 'port', 'unknown')
            host_info = f"{host}:{port}"

        self.logger.info(f"✅ CONNECTION CREATED: {host_info} in {duration:.3f}s")

        # Track connection stats
        if host_info != "unknown":
            if host_info not in self.connection_stats:
                self.connection_stats[host_info] = {"created": 0, "failed": 0, "last_success": None}

            self.connection_stats[host_info]["created"] += 1
            self.connection_stats[host_info]["last_success"] = time.time()

    async def on_dns_resolvehost_start(self, session, trace_config_ctx, params: aiohttp.TraceDnsResolveHostStartParams):
        """Track DNS resolution start"""
        trace_config_ctx.dns_start_time = time.perf_counter()
        host = getattr(params, 'host', 'unknown')
        self.logger.debug(f"🔍 DNS RESOLVE START: {host}")

    async def on_dns_resolvehost_end(self, session, trace_config_ctx, params: aiohttp.TraceDnsResolveHostEndParams):
        """Track DNS resolution completion"""
        duration = time.perf_counter() - trace_config_ctx.dns_start_time
        host = getattr(params, 'host', 'unknown')
        self.logger.debug(f"✅ DNS RESOLVED: {host} in {duration:.3f}s")

    async def on_connection_queued_start(self, session, trace_config_ctx, params):
        """Track when connection is queued (waiting for available connection)"""
        trace_config_ctx.queue_start_time = time.perf_counter()
        # Connection queue params structure may vary
        host_info = getattr(params, 'host', 'unknown') if hasattr(params, 'host') else str(params)
        self.logger.debug(f"⏳ CONNECTION QUEUED: Waiting for available connection to {host_info}")

    async def on_connection_queued_end(self, session, trace_config_ctx, params):
        """Track when connection is acquired from queue"""
        duration = time.perf_counter() - trace_config_ctx.queue_start_time
        self.logger.debug(f"🎯 CONNECTION ACQUIRED: Got connection after {duration:.3f}s queue time")

    def get_trace_config(self) -> TraceConfig:
        """Create and return configured TraceConfig"""
        trace_config = TraceConfig()

        # Request lifecycle
        trace_config.on_request_start.append(self.on_request_start)
        trace_config.on_request_end.append(self.on_request_end)
        trace_config.on_request_exception.append(self.on_request_exception)

        # Connection lifecycle
        trace_config.on_connection_create_start.append(self.on_connection_create_start)
        trace_config.on_connection_create_end.append(self.on_connection_create_end)
        trace_config.on_connection_queued_start.append(self.on_connection_queued_start)
        trace_config.on_connection_queued_end.append(self.on_connection_queued_end)

        # DNS resolution
        trace_config.on_dns_resolvehost_start.append(self.on_dns_resolvehost_start)
        trace_config.on_dns_resolvehost_end.append(self.on_dns_resolvehost_end)

        return trace_config

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics for analysis"""
        return self.connection_stats.copy()


class AioHttpTraceConfig:
    """Robust tracer that handles aiohttp trace parameters correctly"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

    async def on_request_start(self, session, trace_config_ctx, params):
        """Track request start"""
        trace_config_ctx.start_time = time.perf_counter()
        trace_config_ctx.request_url = str(params.url)
        trace_config_ctx.request_method = params.method

        self.logger.debug(f"🚀 REQUEST START: {params.method} {params.url}")

    async def on_request_end(self, session, trace_config_ctx, params):
        """Track successful request completion"""
        duration = time.perf_counter() - trace_config_ctx.start_time

        self.logger.info(
            f"✅ REQUEST SUCCESS: {trace_config_ctx.request_method} {trace_config_ctx.request_url} "
            f"-> {params.response.status} in {duration:.3f}s"
        )

    async def on_request_exception(self, session, trace_config_ctx, params):
        """Track request failures with detailed Windows error analysis"""
        duration = time.perf_counter() - trace_config_ctx.start_time
        error = params.exception
        url = getattr(trace_config_ctx, 'request_url', 'unknown')

        self.logger.error(
            f"❌ REQUEST FAILED: {getattr(trace_config_ctx, 'request_method', 'UNKNOWN')} {url} "
            f"after {duration:.3f}s"
        )

        # Detailed Windows error analysis
        if isinstance(error, aiohttp.ClientConnectorError):
            self.logger.error(f"🔌 CONNECTOR ERROR: {error}")

            # Check for Windows socket errors
            if hasattr(error, 'os_error') and error.os_error:
                os_error = error.os_error
                errno = getattr(os_error, 'errno', None)
                winerror = getattr(os_error, 'winerror', None)

                if winerror == 10053:  # WSAECONNABORTED
                    self.logger.error("   → Windows Error 10053 (WSAECONNABORTED)")
                    self.logger.error("   → Software caused connection abort")
                    self.logger.error("   → Likely causes: Network interruption, firewall, or antivirus interference")

                elif winerror == 10054:  # WSAECONNRESET
                    self.logger.error("   → Windows Error 10054 (WSAECONNRESET)")
                    self.logger.error("   → Connection reset by peer")
                    self.logger.error(
                        "   → Likely causes: Server closed connection, load balancer timeout, network device reset")

                elif winerror == 10060:  # WSAETIMEDOUT
                    self.logger.error("   → Windows Error 10060 (WSAETIMEDOUT)")
                    self.logger.error("   → Connection attempt timed out")
                    self.logger.error("   → Likely causes: Network congestion, server overload, routing issues")

                elif errno or winerror:
                    self.logger.error(f"   → OS Error: errno={errno}, winerror={winerror}")

        elif isinstance(error, (asyncio.TimeoutError, aiohttp.ServerTimeoutError)):
            self.logger.error(f"⏰ TIMEOUT: {type(error).__name__}: {error}")
            self.logger.error("   → Check: Client timeout settings, server response time, network latency")

        elif isinstance(error, aiohttp.ClientResponseError):
            self.logger.error(f"📡 HTTP ERROR: {error.status} {error.message}")

        else:
            self.logger.error(f"❓ UNKNOWN ERROR: {type(error).__name__}: {error}")

    async def on_connection_create_start(self, session, trace_config_ctx, params):
        """Track connection creation start - robust parameter handling"""
        trace_config_ctx.connection_start_time = time.perf_counter()

        # Log available parameters for debugging
        self.logger.debug(f"🔌 CONNECTION CREATE START")
        self.logger.debug(f"   Params type: {type(params)}")
        self.logger.debug(f"   Params attrs: {[attr for attr in dir(params) if not attr.startswith('_')]}")

    async def on_connection_create_end(self, session, trace_config_ctx, params):
        """Track connection creation completion"""
        duration = time.perf_counter() - getattr(trace_config_ctx, 'connection_start_time', time.perf_counter())

        self.logger.info(f"✅ CONNECTION CREATED in {duration:.3f}s")
        self.logger.debug(f"   Params type: {type(params)}")

    async def on_dns_resolvehost_start(self, session, trace_config_ctx, params):
        """Track DNS resolution start"""
        trace_config_ctx.dns_start_time = time.perf_counter()

        # Extract host information safely
        host = "unknown"
        if hasattr(params, 'host'):
            host = params.host
        elif hasattr(params, 'hostname'):
            host = params.hostname

        self.logger.debug(f"🔍 DNS RESOLVE START: {host}")

    async def on_dns_resolvehost_end(self, session, trace_config_ctx, params):
        """Track DNS resolution completion"""
        duration = time.perf_counter() - getattr(trace_config_ctx, 'dns_start_time', time.perf_counter())

        host = "unknown"
        if hasattr(params, 'host'):
            host = params.host
        elif hasattr(params, 'hostname'):
            host = params.hostname

        self.logger.debug(f"✅ DNS RESOLVED: {host} in {duration:.3f}s")

    def get_trace_config(self):
        """Create trace configuration with robust error handling"""
        trace_config = TraceConfig()

        # Core request tracing (most important for your use case)
        trace_config.on_request_start.append(self.on_request_start)
        trace_config.on_request_end.append(self.on_request_end)
        trace_config.on_request_exception.append(self.on_request_exception)

        # Optional connection tracing (may need adjustment based on aiohttp version)
        try:
            trace_config.on_connection_create_start.append(self.on_connection_create_start)
            trace_config.on_connection_create_end.append(self.on_connection_create_end)
        except Exception as e:
            self.logger.warning(f"Could not add connection tracing: {e}")

        try:
            trace_config.on_dns_resolvehost_start.append(self.on_dns_resolvehost_start)
            trace_config.on_dns_resolvehost_end.append(self.on_dns_resolvehost_end)
        except Exception as e:
            self.logger.warning(f"Could not add DNS tracing: {e}")

        return trace_config


