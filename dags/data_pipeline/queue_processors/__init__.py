# coding=utf-8
"""
Queue Processors Package

This package provides a refactored architecture for queue processing operations
following SOLID and DRY principles. It includes:

- Abstract base classes for queue processors
- Configurable batch processing strategies  
- DataFrame processing pipelines
- Enhanced event coordination
- Concrete implementations for upsert operations

Key Components:
- AbstractQueueProcessor: Base class for all queue processors
- BatchProcessingStrategy: Configurable strategies for batch processing
- DataFrameProcessingPipeline: Reusable DataFrame processing components
- EnhancedEventCoordinator: Improved event coordination for referential integrity
- UpsertQueueProcessor: Refactored process_upsert_queue implementation
- UpsertOthersProcessor: Refactored process_upsert_others implementation

Usage:
    from dags.data_pipeline.queue_processors import (
        create_upsert_queue_processor,
        create_upsert_others_processor,
        process_upsert_queue_refactored,
        process_upsert_others_refactored
    )
"""

from .base_queue_processor import (
    AbstractQueueProcessor,
    BatchProcessingConfig,
    QueueProcessingResult,
    DataFramePreprocessor
)

from .batch_processing_strategy import (
    BatchProcessingStrategy,
    MessageCountStrategy,
    RecordCountStrategy,
    EventBasedStrategy,
    CompositeStrategy,
    AdaptiveStrategy,
    create_upsert_queue_strategy,
    create_upsert_others_strategy
)

from .dataframe_pipeline import (
    DataFrameProcessingPipeline,
    PreprocessingProcessor,
    ValidationProcessor,
    ConsolidationProcessor,
    ProcessingResult,
    ProcessingStage,
    create_standard_pipeline,
    create_consolidation_pipeline
)

from .event_coordination import (
    EnhancedEventCoordinator,
    ReferentialIntegrityCoordinator,
    ProcessingPhase,
    CoordinationState,
    enhanced_event_coordinator,
    referential_integrity_coordinator
)

from .concrete_processors import (
    UpsertQueueProcessor,
    UpsertOthersProcessor,
    create_upsert_queue_processor,
    create_upsert_others_processor,
    process_upsert_queue_refactored,
    process_upsert_others_refactored
)

__all__ = [
    # Base classes
    "AbstractQueueProcessor",
    "BatchProcessingConfig", 
    "QueueProcessingResult",
    "DataFramePreprocessor",
    
    # Batch processing strategies
    "BatchProcessingStrategy",
    "MessageCountStrategy",
    "RecordCountStrategy", 
    "EventBasedStrategy",
    "CompositeStrategy",
    "AdaptiveStrategy",
    "create_upsert_queue_strategy",
    "create_upsert_others_strategy",
    
    # DataFrame processing
    "DataFrameProcessingPipeline",
    "PreprocessingProcessor",
    "ValidationProcessor",
    "ConsolidationProcessor",
    "ProcessingResult",
    "ProcessingStage",
    "create_standard_pipeline",
    "create_consolidation_pipeline",
    
    # Event coordination
    "EnhancedEventCoordinator",
    "ReferentialIntegrityCoordinator",
    "ProcessingPhase",
    "CoordinationState",
    "enhanced_event_coordinator",
    "referential_integrity_coordinator",
    
    # Concrete processors
    "UpsertQueueProcessor",
    "UpsertOthersProcessor",
    "create_upsert_queue_processor",
    "create_upsert_others_processor",
    "process_upsert_queue_refactored",
    "process_upsert_others_refactored"
]
