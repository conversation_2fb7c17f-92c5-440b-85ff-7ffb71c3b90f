#!/usr/bin/env python3
"""
Example showing how to use integrated LoggerContainer and DatabaseContainer
in your utility_code.py or other modules.
"""

import os
import asyncio
import logging
from pathlib import Path
from dags.data_pipeline.containers import Logger<PERSON>ontainer, DatabaseContainer


async def example_integrated_usage():
    """
    Example of how to use the integrated containers in your code.
    """
    
    # 1. Setup environment (you can do this in your main script or environment)
    log_directory = Path("c:/vishal/log") if os.name == 'nt' else Path("/tmp/airflow_logs")
    log_directory.mkdir(parents=True, exist_ok=True)
    os.environ["LOG_FILE_PATH"] = str(log_directory)
    os.environ["ENVIRONMENT"] = "development"  # or "staging", "production"
    
    print(f"Log files will be written to: {log_directory}")
    
    # 2. Initialize containers (similar to your current utility_code.py)
    logger_container = LoggerContainer()
    logger_container.wire(modules=["dags.data_pipeline.utility_code", "__main__"])
    logger_container.init_resources()
    
    db_container = DatabaseContainer()
    db_container.wire(modules=["dags.data_pipeline.utility_code", "__main__"])
    
    print("✅ Containers initialized with integrated logging")
    
    # 3. Get loggers and session managers
    main_logger = logger_container.logger()
    debug_logger = logger_container.debug_logger()
    
    async_managers = db_container.async_session_managers()
    
    # 4. Use the integrated logging
    main_logger.info("Starting database operations...")
    debug_logger.debug("Debug information about the process")
    
    # 5. Database operations with integrated logging
    try:
        async with async_managers["public"].async_session() as session:
            # The session manager will use the same logger as logger_container
            main_logger.info("Database session created successfully")
            
            # Your database operations here
            result = await session.execute("SELECT 1 as test")
            row = result.fetchone()
            main_logger.info(f"Database test query result: {row}")
            
    except Exception as e:
        main_logger.error(f"Database operation failed: {e}")
    
    # 6. Show that all loggers are integrated
    session_logger = async_managers["public"].logger
    main_logger.info(f"Session logger name: {session_logger.name}")
    main_logger.info(f"Main logger name: {main_logger.name}")
    main_logger.info(f"Loggers are same instance: {session_logger is main_logger}")
    
    return logger_container, db_container


def show_log_level_examples():
    """
    Show examples of controlling log levels.
    """
    
    print("\n" + "="*50)
    print("LOG LEVEL CONTROL EXAMPLES")
    print("="*50)
    
    # Method 1: Environment-based
    print("Method 1: Environment-based control")
    print("os.environ['ENVIRONMENT'] = 'development'  # DEBUG level")
    print("os.environ['ENVIRONMENT'] = 'staging'      # INFO level")
    print("os.environ['ENVIRONMENT'] = 'production'   # WARNING level")
    print()
    
    # Method 2: Programmatic control
    print("Method 2: Programmatic control")
    print("logger = logger_container.logger()")
    print("logger.setLevel(logging.DEBUG)")
    print()
    
    # Method 3: Handler-specific control
    print("Method 3: Handler-specific control")
    print("for handler in logger.handlers:")
    print("    handler.setLevel(logging.INFO)")
    print()


def demonstrate_different_loggers():
    """
    Demonstrate the different types of loggers available.
    """
    
    print("\n" + "="*50)
    print("AVAILABLE LOGGER TYPES")
    print("="*50)
    
    logger_container = LoggerContainer()
    logger_container.init_resources()
    
    # Main application logger (environment-specific)
    main_logger = logger_container.logger()
    print(f"Main logger: {main_logger.name} (level: {logging.getLevelName(main_logger.level)})")
    
    # Profiled logger for performance monitoring
    profiled_logger = logger_container.profiled_logger()
    print(f"Profiled logger: {profiled_logger.name} (level: {logging.getLevelName(profiled_logger.level)})")
    
    # Debug logger for debugging operations
    debug_logger = logger_container.debug_logger()
    print(f"Debug logger: {debug_logger.name} (level: {logging.getLevelName(debug_logger.level)})")
    
    # Debugging monitor logger
    monitor_logger = logger_container.debugging_monitor_logger()
    print(f"Monitor logger: {monitor_logger.name} (level: {logging.getLevelName(monitor_logger.level)})")
    
    # Test each logger
    print("\nTesting each logger:")
    main_logger.info("Main logger test message")
    profiled_logger.info("Profiled logger test message")
    debug_logger.debug("Debug logger test message")
    monitor_logger.info("Monitor logger test message")


def show_file_locations():
    """
    Show where log files are located based on current configuration.
    """
    
    print("\n" + "="*50)
    print("LOG FILE LOCATIONS")
    print("="*50)
    
    log_file_path = os.getenv("LOG_FILE_PATH")
    
    if log_file_path:
        log_dir = Path(log_file_path)
        print(f"Log directory: {log_dir}")
        print(f"Directory exists: {log_dir.exists()}")
        
        # Expected log files from logging_config.yaml
        expected_files = [
            "main.log",
            "profiled.log"
        ]
        
        print("\nExpected log files:")
        for filename in expected_files:
            filepath = log_dir / filename
            exists = filepath.exists()
            size = filepath.stat().st_size if exists else 0
            print(f"  {filename}: {'✅' if exists else '❌'} ({size} bytes)")
    
    else:
        print("LOG_FILE_PATH not set. Using default locations:")
        if os.name == 'nt':
            print("  Windows default: c:/vishal/log/")
        else:
            print("  Linux default: /tmp/dataframe_debug/")


async def main():
    """
    Main demonstration function.
    """
    
    print("INTEGRATED LOGGING DEMONSTRATION")
    print("="*50)
    
    # Show available logger types
    demonstrate_different_loggers()
    
    # Show log level control examples
    show_log_level_examples()
    
    # Show file locations
    show_file_locations()
    
    # Demonstrate integrated usage
    print("\n" + "="*50)
    print("INTEGRATED USAGE EXAMPLE")
    print("="*50)
    
    try:
        logger_container, db_container = await example_integrated_usage()
        
        print("\n✅ SUCCESS: Integrated logging is working!")
        print("✅ LoggerContainer and DatabaseContainer share the same logger")
        print("✅ All database operations will use centralized logging")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure you have:")
        print("  - PostgreSQL database accessible")
        print("  - KeePass database configured")
        print("  - Required environment variables set")
    
    print("\n" + "="*50)
    print("USAGE IN YOUR CODE")
    print("="*50)
    print("# In your utility_code.py:")
    print("logger_container = LoggerContainer()")
    print("logger_container.wire(modules=['dags.data_pipeline.utility_code'])")
    print("logger_container.init_resources()")
    print()
    print("db_container = DatabaseContainer()")
    print("db_container.wire(modules=['dags.data_pipeline.utility_code', __name__])")
    print()
    print("# Now both containers share the same logger!")
    print("logger = logger_container.logger()")
    print("async_managers = db_container.async_session_managers()")


if __name__ == "__main__":
    asyncio.run(main())
