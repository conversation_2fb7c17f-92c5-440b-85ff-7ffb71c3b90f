#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to demonstrate logging setup and environment configuration.
Shows how to control log file locations and log levels.
"""

import os
import logging
from pathlib import Path
from dags.data_pipeline.containers import LoggerContainer, DatabaseContainer


def setup_logging_environment():
    """
    Setup logging environment with proper paths and levels.
    """
    
    # 1. Set LOG_FILE_PATH environment variable
    # This controls where log files are written
    log_directory = Path("c:/vishal/log") if os.name == 'nt' else Path("/tmp/airflow_logs")
    log_directory.mkdir(parents=True, exist_ok=True)
    
    os.environ["LOG_FILE_PATH"] = str(log_directory)
    print(f"✅ LOG_FILE_PATH set to: {log_directory}")
    
    # 2. Set logging configuration path (optional)
    config_path = Path("dags/data_pipeline/logging_config.yaml")
    if config_path.exists():
        os.environ["LOGGING_CONFIG_PATH"] = str(config_path)
        print(f"✅ LOGGING_CONFIG_PATH set to: {config_path}")
    
    # 3. Set environment for logger selection
    # This determines which logger configuration is used from logging_config.yaml
    environment = os.getenv("ENVIRONMENT", "development")  # development, staging, production
    print(f"✅ Environment set to: {environment}")
    
    return log_directory


def demonstrate_logger_integration():
    """
    Demonstrate how LoggerContainer and DatabaseContainer work together.
    """
    
    print("\n" + "="*60)
    print("DEMONSTRATING LOGGER INTEGRATION")
    print("="*60)
    
    # Initialize LoggerContainer first
    logger_container = LoggerContainer()
    logger_container.wire(modules=["__main__"])
    logger_container.init_resources()
    
    print("✅ LoggerContainer initialized")
    
    # Initialize DatabaseContainer (now uses LoggerContainer's logger)
    db_container = DatabaseContainer()
    db_container.wire(modules=["__main__"])
    
    print("✅ DatabaseContainer initialized with shared logger")
    
    # Get a session manager to see the logger in action
    try:
        async_managers = db_container.async_session_managers()
        public_manager = async_managers["public"]
        
        # The logger from the session manager should now be the same as LoggerContainer's logger
        session_logger = public_manager.logger
        container_logger = logger_container.logger()
        
        print(f"✅ Session manager logger: {session_logger.name}")
        print(f"✅ Container logger: {container_logger.name}")
        print(f"✅ Loggers are same instance: {session_logger is container_logger}")
        
        # Test logging
        session_logger.info("Test message from session manager logger")
        container_logger.info("Test message from container logger")
        
    except Exception as e:
        print(f"❌ Error demonstrating logger integration: {e}")
    
    return logger_container, db_container


def show_log_level_control():
    """
    Show different ways to control log levels.
    """
    
    print("\n" + "="*60)
    print("LOG LEVEL CONTROL OPTIONS")
    print("="*60)
    
    print("1. Environment Variable Method:")
    print("   Set ENVIRONMENT=development (DEBUG level)")
    print("   Set ENVIRONMENT=staging (INFO level)")  
    print("   Set ENVIRONMENT=production (WARNING level)")
    print()
    
    print("2. Programmatic Method:")
    print("   logger = logging.getLogger('your_logger_name')")
    print("   logger.setLevel(logging.DEBUG)  # or INFO, WARNING, ERROR")
    print()
    
    print("3. Handler-specific Method:")
    print("   handler = logger.handlers[0]")
    print("   handler.setLevel(logging.INFO)")
    print()
    
    print("4. Configuration File Method:")
    print("   Edit logging_config.yaml:")
    print("   handlers:")
    print("     console:")
    print("       level: DEBUG  # Change to INFO, WARNING, etc.")
    print()


def show_current_configuration():
    """
    Show current logging configuration.
    """
    
    print("\n" + "="*60)
    print("CURRENT LOGGING CONFIGURATION")
    print("="*60)
    
    # Environment variables
    log_file_path = os.getenv("LOG_FILE_PATH", "Not set")
    logging_config_path = os.getenv("LOGGING_CONFIG_PATH", "Not set")
    environment = os.getenv("ENVIRONMENT", "Not set")
    
    print(f"LOG_FILE_PATH: {log_file_path}")
    print(f"LOGGING_CONFIG_PATH: {logging_config_path}")
    print(f"ENVIRONMENT: {environment}")
    print()
    
    # Expected log file locations
    if log_file_path != "Not set":
        main_log = Path(log_file_path) / "main.log"
        profiled_log = Path(log_file_path) / "profiled.log"
        
        print("Expected log files:")
        print(f"  Main log: {main_log}")
        print(f"  Profiled log: {profiled_log}")
        print(f"  Main log exists: {main_log.exists()}")
        print(f"  Profiled log exists: {profiled_log.exists()}")
    
    print()
    
    # Root logger configuration
    root_logger = logging.getLogger()
    print(f"Root logger level: {logging.getLevelName(root_logger.level)}")
    print(f"Root logger handlers: {len(root_logger.handlers)}")
    
    for i, handler in enumerate(root_logger.handlers):
        print(f"  Handler {i}: {type(handler).__name__} (level: {logging.getLevelName(handler.level)})")


def main():
    """
    Main demonstration function.
    """
    
    print("AIRFLOW LOGGING SETUP DEMONSTRATION")
    print("="*60)
    
    # Setup environment
    log_directory = setup_logging_environment()
    
    # Show current configuration
    show_current_configuration()
    
    # Show log level control options
    show_log_level_control()
    
    # Demonstrate logger integration
    try:
        logger_container, db_container = demonstrate_logger_integration()
        
        print("\n" + "="*60)
        print("INTEGRATION SUCCESS!")
        print("="*60)
        print("✅ LoggerContainer and DatabaseContainer are now integrated")
        print("✅ All database session managers use the same logger instance")
        print("✅ Log levels can be controlled centrally")
        print(f"✅ Log files will be written to: {log_directory}")
        
    except Exception as e:
        print(f"\n❌ Integration failed: {e}")
        print("Make sure you have the required dependencies and configuration files.")
    
    print("\n" + "="*60)
    print("NEXT STEPS")
    print("="*60)
    print("1. Set LOG_FILE_PATH environment variable to control log location")
    print("2. Set ENVIRONMENT variable to control log level (development/staging/production)")
    print("3. Modify logging_config.yaml for fine-grained control")
    print("4. Use the integrated containers in your utility_code.py")


if __name__ == "__main__":
    main()
