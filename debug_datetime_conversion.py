import pandas as pd

def main():
    df = pd.read_json(r"C:\Users\<USER>\Downloads\response_resolution.json")
    df = pd.json_normalize(df['issues'])
    df.drop(columns=['expand', 'self'], inplace=True)
    df.to_csv(r"C:\Users\<USER>\Downloads\response.csv", index=False)
    if 'fields.created' in df.columns:
        df['created'] = pd.to_datetime(df['fields.created'], format='%Y-%m-%dT%H:%M:%S.%f%z', utc=True)
    elif 'fields.resolutiondate' in df.columns:
        df['resolutiondate'] = pd.to_datetime(df['fields.resolutiondate'], format='%Y-%m-%dT%H:%M:%S.%f%z', utc=True)
    df.to_csv(r"C:\Users\<USER>\Downloads\response_processed.csv", index=False)

if __name__ == '__main__':
    main()